#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取08-问题文件夹中三个表格的F列数据并保存为MD格式
"""

import pandas as pd
import os
from datetime import datetime

def extract_f_column_to_md():
    """
    提取东海、南海、黄渤海三个表格的F列数据并保存为MD格式
    """
    # 设置工作目录
    base_dir = os.getcwd()
    problem_dir = os.path.join(base_dir, "08-问题")
    
    # 定义文件路径和对应的名称
    files_info = [
        {
            "file_path": "01-东海当面及邻近海域演习（GN-001）.xlsx",
            "name": "东海",
            "output_file": "东海_F列数据.md"
        },
        {
            "file_path": "02-南海当面及邻近海域演习（GN-002）.xlsx", 
            "name": "南海",
            "output_file": "南海_F列数据.md"
        },
        {
            "file_path": "03-黄渤海当面及邻近海域演习（GN-003） - 副本.xlsx",
            "name": "黄渤海", 
            "output_file": "黄渤海_F列数据.md"
        }
    ]
    
    print("开始提取F列数据...")
    
    for file_info in files_info:
        try:
            # 构建完整文件路径
            full_path = os.path.join(problem_dir, file_info["file_path"])
            
            # 检查文件是否存在
            if not os.path.exists(full_path):
                print(f"警告: 文件不存在 - {full_path}")
                continue
                
            print(f"正在处理: {file_info['name']} - {file_info['file_path']}")
            
            # 读取Excel文件
            df = pd.read_excel(full_path)
            
            # 提取F列数据（第6列，索引为5）
            if df.shape[1] >= 6:  # 确保至少有6列
                f_column_data = df.iloc[:, 5]  # F列是第6列
                
                # 过滤掉空值和NaN
                valid_data = []
                for item in f_column_data:
                    if pd.notna(item) and str(item).strip() != '':
                        valid_data.append(str(item).strip())
                
                # 生成MD文件内容
                md_content = generate_md_content(file_info["name"], valid_data)
                
                # 保存MD文件
                output_path = file_info["output_file"]
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(md_content)
                
                print(f"✓ 已保存: {output_path} (共{len(valid_data)}条有效数据)")
                
            else:
                print(f"警告: {file_info['name']} 文件列数不足，无法提取F列")
                
        except Exception as e:
            print(f"错误: 处理 {file_info['name']} 时出现异常 - {str(e)}")
    
    print("\n提取完成！")

def generate_md_content(region_name, data_list):
    """
    生成MD格式的内容

    Args:
        region_name (str): 区域名称（东海、南海、黄渤海）
        data_list (list): F列的数据列表

    Returns:
        str: MD格式的内容
    """
    md_content = f"# {region_name}\n\n"

    if data_list:
        for item in data_list:
            md_content += f"- {item}\n"
    else:
        md_content += "- 无数据\n"

    return md_content

if __name__ == "__main__":
    extract_f_column_to_md()
