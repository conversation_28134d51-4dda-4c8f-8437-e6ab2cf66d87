#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新CSV文件中photo列的路径格式
将图片名称匹配到演习照片文件夹中的实际文件，并更新为指定格式
"""

import os
import csv
import shutil
from datetime import datetime
from difflib import SequenceMatcher

def get_image_files(photo_dir="演习照片"):
    """
    获取演习照片文件夹中的所有图片文件
    
    Args:
        photo_dir (str): 图片文件夹路径
        
    Returns:
        list: 图片文件名列表
    """
    if not os.path.exists(photo_dir):
        print(f"警告: 图片文件夹 {photo_dir} 不存在")
        return []
    
    image_extensions = {'.jpg', '.jpeg', '.png', '.webp', '.avif', '.JPG', '.JPEG', '.PNG'}
    image_files = []
    
    for filename in os.listdir(photo_dir):
        if any(filename.endswith(ext) for ext in image_extensions):
            image_files.append(filename)
    
    print(f"找到 {len(image_files)} 个图片文件")
    return image_files

def similarity(a, b):
    """计算两个字符串的相似度"""
    return SequenceMatcher(None, a.lower(), b.lower()).ratio()

def find_best_match(photo_name, image_files, threshold=0.6):
    """
    为photo名称找到最佳匹配的图片文件
    
    Args:
        photo_name (str): CSV中的photo名称
        image_files (list): 图片文件列表
        threshold (float): 相似度阈值
        
    Returns:
        str or None: 匹配的图片文件名，如果没有找到则返回None
    """
    if not photo_name or photo_name.strip() == "" or photo_name.strip() == "暂无":
        return None
    
    best_match = None
    best_score = 0
    
    # 清理photo_name，移除可能的扩展名
    clean_photo_name = photo_name.strip()
    if clean_photo_name.lower().endswith(('.jpg', '.jpeg', '.png', '.webp', '.avif')):
        clean_photo_name = os.path.splitext(clean_photo_name)[0]
    
    for image_file in image_files:
        # 移除扩展名进行比较
        image_name = os.path.splitext(image_file)[0]
        
        # 计算相似度
        score = similarity(clean_photo_name, image_name)
        
        if score > best_score and score >= threshold:
            best_score = score
            best_match = image_file
    
    return best_match

def update_photo_column(csv_file_path, output_dir="已修改2", photo_dir="演习照片"):
    """
    更新CSV文件中的photo列
    
    Args:
        csv_file_path (str): CSV文件路径
        output_dir (str): 输出目录
        photo_dir (str): 图片文件夹路径
    """
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"已创建输出目录: {output_dir}")
    
    # 创建备份文件
    backup_path = csv_file_path + f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(csv_file_path, backup_path)
    print(f"已创建备份文件: {backup_path}")
    
    # 获取图片文件列表
    image_files = get_image_files(photo_dir)
    if not image_files:
        print("没有找到图片文件，跳过处理")
        return 0, 0
    
    # 读取CSV文件
    with open(csv_file_path, 'r', encoding='utf-8') as file:
        csv_reader = csv.reader(file)
        rows = list(csv_reader)
    
    if len(rows) == 0:
        print("CSV文件为空")
        return 0, 0
    
    # 处理数据（跳过表头）
    updated_count = 0
    matched_count = 0
    
    print(f"\n开始处理photo列...")
    print("-" * 60)
    
    for i in range(1, len(rows)):  # 从第2行开始（跳过表头）
        if len(rows[i]) >= 16:  # 确保有photo列（第16列，索引15）
            original_photo = rows[i][15].strip()
            
            if original_photo and original_photo != "暂无":
                # 查找匹配的图片文件
                matched_file = find_best_match(original_photo, image_files)
                
                if matched_file:
                    # 更新为指定格式：演习照片\文件名
                    new_photo_path = f'{photo_dir}\\{matched_file}'
                    rows[i][15] = new_photo_path
                    matched_count += 1

                    print(f"行{i+1}: '{original_photo}' -> '{new_photo_path}'")
                else:
                    print(f"行{i+1}: '{original_photo}' -> 未找到匹配")
                
                updated_count += 1
    
    # 生成输出文件路径
    filename = os.path.basename(csv_file_path)
    output_file_path = os.path.join(output_dir, filename)
    
    # 写入输出文件
    with open(output_file_path, 'w', encoding='utf-8', newline='') as file:
        csv_writer = csv.writer(file)
        csv_writer.writerows(rows)
    
    print(f"\n处理完成！")
    print(f"总共检查了 {updated_count} 个photo字段")
    print(f"成功匹配了 {matched_count} 个图片")
    print(f"文件已保存到: {output_file_path}")
    
    return updated_count, matched_count, output_file_path

def main():
    """主函数"""
    # 定义要处理的CSV文件列表
    csv_files = [
        "已修改1/东海.csv",
        "已修改1/南海.csv", 
        "已修改1/黄渤海.csv"
    ]
    
    print("开始更新photo列路径...")
    print("=" * 60)
    
    total_checked = 0
    total_matched = 0
    successful_files = []
    failed_files = []
    
    for csv_file_path in csv_files:
        print(f"\n处理文件: {csv_file_path}")
        print("-" * 40)
        
        # 检查文件是否存在
        if not os.path.exists(csv_file_path):
            print(f"警告: 文件 {csv_file_path} 不存在，跳过")
            failed_files.append(csv_file_path)
            continue
        
        try:
            # 执行更新
            checked_count, matched_count, output_file_path = update_photo_column(csv_file_path)
            total_checked += checked_count
            total_matched += matched_count
            successful_files.append(output_file_path)
            
        except Exception as e:
            print(f"错误: 处理文件 {csv_file_path} 时出错: {e}")
            failed_files.append(csv_file_path)
    
    # 总结报告
    print("\n" + "=" * 60)
    print("批量photo路径更新完成！")
    print(f"成功处理文件数: {len(successful_files)}")
    print(f"失败文件数: {len(failed_files)}")
    print(f"总共检查photo字段: {total_checked} 个")
    print(f"成功匹配图片: {total_matched} 个")
    
    if successful_files:
        print(f"\n成功处理的文件:")
        for file in successful_files:
            print(f"  ✓ {file}")
    
    if failed_files:
        print(f"\n处理失败的文件:")
        for file in failed_files:
            print(f"  ✗ {file}")
    
    print("\n说明:")
    print("- photo列已更新为格式：演习照片\\图片文件名")
    print("- 使用字符串相似度匹配算法找到最佳匹配的图片文件")
    print("- 原始文件已创建备份")
    print("- 更新后的文件保存在'已修改2'文件夹中")

if __name__ == "__main__":
    main()
