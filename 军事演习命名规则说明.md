# 军事演习命名规则说明文档

## 概述

本文档基于国际军事演习命名标准，制定了军事演习数据库中演习名称的标准化规则。目标是移除冗余信息，保持专业性，提高数据一致性。

## 国际军事演习命名标准参考

### NATO（北约）命名规范
- **第一词**：标识负责指挥部（如STEADFAST表示SHAPE）
- **第二词**：表示演习功能/领域（如J开头表示联合演习）
- **原则**：简洁、专业、避免冒犯性词汇

### 美军命名原则
- **1989年后**：使用有意义名称替代随机代号
- **要求**：积极正面、避免轻浮、体现任务特征
- **系统**：使用NICKA系统避免重复

### 其他国家实践
- **英国**：使用古代英雄、神话人物、星座名称
- **中国**：地理+功能描述，如"友谊-2004"

## 本项目清理规则

### 1. 移除冗余日期信息

**原则**：演习时间信息已在`exercise_time`字段中，名称中的日期为冗余信息

**清理模式**：
- `2021年4月5日美日双边演习` → `美日双边演习`
- `2022年11月19至20日日美澳三边演习` → `日美澳三边演习`
- `2024年5月15至17日美日双边演习` → `美日双边演习`

### 2. 标准化演习代号

**原则**：保留核心代号，移除年份标识

**清理模式**：
- `美日"利剑23"演习` → `美日"利剑"演习`
- `美日"利剑24"演习` → `美日"利剑"演习`
- `"铁拳2024"演习` → `"铁拳"演习`
- `"对抗北方24"演习` → `"对抗北方"演习`

### 3. 清理编号系统

**原则**：移除具体编号，保留演习类型

**清理模式**：
- `台湾"汉光37号"演习` → `台湾"汉光"演习`
- `台湾"汉光38号"演习` → `台湾"汉光"演习`
- `台2021空军"天龙操演"演习` → `台空军"天龙操演"演习`

### 4. 标准化训练测考名称

**原则**：简化复杂的编号系统

**清理模式**：
- `台"三军联合作战训练测考109之7号、8号"操演` → `台"三军联合作战训练测考"操演`
- `台"三军联合作战训练测考110之1号、2号"操演` → `台"三军联合作战训练测考"操演`

### 5. 统一格式标准

**原则**：统一引号使用，清理多余空格

**清理模式**：
- 统一使用英文双引号 `"`
- 移除多余空格
- 清理首尾空白字符

## 保留原则

### 保留的信息
1. **核心演习标识**：如"利剑"、"铁拳"、"汉光"等
2. **地理信息**：如"东海"、"太平洋"、"南海"等
3. **参与方信息**：如"美日"、"多边"、"台湾"等
4. **演习类型**：如"联合"、"双边"、"多边"等
5. **功能描述**：如"反潜"、"登陆"、"防空"等

### 不保留的信息
1. **具体年份**：如"21"、"23"、"2024"等
2. **具体日期**：如"4月5日"、"11月19至20日"等
3. **序号编号**：如"37号"、"109之7号"等
4. **重复的年份标识**：如"2021年"前缀等

## 清理效果示例

### 美日演习系列
- **清理前**：`美日"利剑23"演习`、`美日"利剑24"演习`
- **清理后**：`美日"利剑"演习`
- **效果**：统一了同一系列演习的名称，便于分析

### 台湾演习系列
- **清理前**：`台湾"汉光37号"演习`、`台湾"汉光38号"演习`、`台湾"汉光39号"演习`
- **清理后**：`台湾"汉光"演习`
- **效果**：突出演习本质，减少版本差异

### 训练测考系列
- **清理前**：`台"三军联合作战训练测考109之7号、8号"操演`
- **清理后**：`台"三军联合作战训练测考"操演`
- **效果**：简化复杂编号，提高可读性

## 技术实现

### 正则表达式模式
```python
# 移除日期前缀
r'^(\d{4}年\d{1,2}月\d{1,2}日)'

# 移除演习代号中的年份
r'(利剑|铁拳|对抗北方)\s*\d{2,4}'

# 移除编号中的年份
r'(汉光|天龙操演)\s*\d{2,4}号?'

# 标准化训练测考编号
r'"三军联合作战训练测考\d{3}之\d+号[、，]\d*号?"'
```

### 处理优先级
1. 移除具体日期前缀
2. 处理演习代号中的年份
3. 清理编号系统
4. 标准化格式
5. 最终清理空格

## 质量保证

### 验证机制
1. **变更记录**：记录每个变更的详细信息
2. **统计报告**：提供清理前后的对比统计
3. **规则追踪**：记录应用的具体规则
4. **备份机制**：保留原始数据备份

### 输出文件
- **清理后的CSV文件**：保存在"已修改3"文件夹
- **清理报告**：详细记录所有变更
- **统计信息**：提供清理效果概览

## 使用说明

### 运行程序
```bash
python clean_exercise_names.py
```

### 输入要求
- 输入文件夹：`已修改2`
- 文件格式：CSV文件，包含`exercise_name`列

### 输出结果
- 输出文件夹：`已修改3`
- 清理报告：`演习名称清理报告.txt`

## 注意事项

1. **数据完整性**：清理过程不会丢失关键信息
2. **可逆性**：保留原始数据，支持回滚
3. **一致性**：确保同类演习名称的统一性
4. **专业性**：遵循国际军事演习命名标准

## 后续维护

### 规则更新
- 根据新的演习命名模式调整规则
- 定期审查清理效果
- 优化正则表达式性能

### 质量监控
- 定期检查清理结果
- 收集用户反馈
- 持续改进清理算法
