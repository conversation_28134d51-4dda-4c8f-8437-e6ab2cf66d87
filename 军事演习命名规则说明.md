# 军事演习命名规则与标准化规范

## 一、国际军事演习命名标准体系

### NATO（北约）命名规范

**基本结构**：[指挥部标识词] + [功能领域词]

**指挥部标识词**：

- STEADFAST - 欧洲盟军最高司令部（SHAPE）
- TRIDENT - 盟军转型司令部
- BRILLIANT - 联合部队司令部布伦瑟姆
- NOBLE - 联合部队司令部那不勒斯
- DYNAMIC - 盟军海上司令部

**功能领域词首字母规则**：

- J开头 - 联合演习（多领域）
- A开头 - 空中作战
- M开头 - 海上作战
- L开头 - 陆地作战
- C开头 - 指挥控制
- I开头 - 情报

**标准格式**：STEADFAST JUPITER（联合演习）、DYNAMIC MONGOOSE（海上作战）

### 美军命名体系

**命名原则**：

1. 积极正面，避免冒犯性词汇
2. 体现任务特征和作战意图
3. 便于公众理解和媒体传播
4. 通过NICKA系统避免重复

**格式类型**：

- [地理特征] + [行动性质]：Desert Storm、Enduring Freedom
- [价值理念] + [行动目标]：Just Cause、Iraqi Freedom
- [代号名称] + [年份标识]：Keen Sword 23、Iron Fist 24

### 英军命名传统

**丘吉尔原则**（1943年确立）：

- 使用古代英雄名称：HERCULES、ACHILLES
- 希腊罗马神话人物：NEPTUNE、APOLLO
- 星座和天体名称：ORION、POLARIS
- 著名战争英雄：WELLINGTON、NELSON

**格式规范**：单一词汇或[神话人物] + [数字编号]

## 二、东亚地区演习命名特色

### 美日联合演习

**双语对照格式**：[中文名称] + [英文对应] + [年份/编号]

- 标准格式：美日"利剑"演习 / Keen Sword
- 系列标识：Iron Fist（铁拳）、Cope North（对抗北方）

### 中国军事演习

**地理+功能格式**：[地理位置] + [演习性质] + [年份标识]

- 双边合作：友谊-2004、和平使命-2021
- 地域标识：东海演习、南海演习
- 功能描述：联合反恐、海上联合

### 台湾地区演习

**传统文化格式**：

- 历史典故：汉光（汉朝光武帝）
- 地理象征：天龙（台湾地形）
- 季节标识：春节战备操演
- 编号系统：三军联合作战训练测考 + 数字编码

### 日本自卫队演习

**年号+性质格式**：

- 令和6年度陆上自卫队演习
- [参与方] + [功能] + [年份]：日美双边演习

## 三、演习名称标准化规范

### 标准化原则

1. **保留核心标识**：演习的本质特征和品牌价值
2. **移除时间冗余**：年份、具体日期、序号编码
3. **统一格式标准**：引号使用、空格处理、字符规范
4. **维护系列完整性**：同一系列演习使用统一名称

### 标准化格式规范

**基本格式**：[参与方] + [演习核心名称] + [演习类型]

**参与方标识**：

- 双边：美日、日澳、美韩
- 多边：美日澳、美日澳菲
- 单边：台湾、日本

**演习核心名称**：

- 保留引号内的代号："利剑"、"铁拳"、"汉光"
- 保留地理标识：东海、太平洋、南海
- 保留功能描述：联合、反潜、登陆

**演习类型**：

- 演习、操演、训练、测考

### 清理规则体系

**第一级：日期信息清理**

- 移除格式：YYYY年MM月DD日
- 移除格式：YYYY年MM月DD至DD日
- 保留：演习时间字段中的完整时间信息

**第二级：年份标识清理**

- 移除格式：演习名称中的"21"、"22"、"23"、"24"等年份后缀
- 移除格式：编号中的年份标识如"37号"、"38号"
- 保留：年份作为演习系列区分的核心标识

**第三级：编号系统简化**

- 复杂编号：三军联合作战训练测考109之7号、8号 → 三军联合作战训练测考
- 序列编号：汉光37号、汉光38号 → 汉光
- 版本标识：太平洋皇冠21-1、21-2 → 太平洋皇冠

**第四级：格式标准化**

- 引号统一：使用英文双引号""
- 空格清理：移除多余空格，保留必要分隔
- 字符规范：统一中英文标点符号使用

### 质量控制标准

**数据完整性检查**：

- 核心演习标识不丢失
- 参与方信息保持准确
- 演习性质特征保留

**一致性验证**：

- 同系列演习名称统一
- 格式规范应用一致
- 清理规则执行完整

**可追溯性保障**：

- 完整的变更记录
- 清理规则应用日志
- 原始数据备份机制
