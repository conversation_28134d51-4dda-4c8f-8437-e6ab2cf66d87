#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复photo列和data_sources列中URL格式错误的程序
将 https:-- 和 http:-- 修复为正确的 https:// 和 http://
"""

import pandas as pd
import os
import re
from datetime import datetime

class AllURLFixer:
    def __init__(self):
        self.input_folder = "已修改4"
        self.output_folder = "已修改5"
        self.files_to_process = ["东海.csv", "南海.csv", "黄渤海.csv"]
        
    def create_output_folder(self):
        """创建输出文件夹"""
        if not os.path.exists(self.output_folder):
            os.makedirs(self.output_folder)
            print(f"创建输出文件夹: {self.output_folder}")
    
    def fix_url_format(self, text):
        """修复URL格式错误"""
        if pd.isna(text) or text == "":
            return text
        
        # 将 https:-- 替换为 https://
        text = re.sub(r'https:--', 'https://', text)
        # 将 http:-- 替换为 http://
        text = re.sub(r'http:--', 'http://', text)
        
        return text
    
    def count_url_errors(self, series):
        """统计URL格式错误数量"""
        if series is None:
            return {'https_errors': 0, 'http_errors': 0}
        
        data = series.astype(str)
        https_errors = data.str.contains('https:--', na=False).sum()
        http_errors = data.str.contains('http:--', na=False).sum()
        
        return {
            'https_errors': https_errors,
            'http_errors': http_errors
        }
    
    def process_single_file(self, filename):
        """处理单个CSV文件"""
        input_path = os.path.join(self.input_folder, filename)
        output_path = os.path.join(self.output_folder, filename)
        
        print(f"\n处理文件: {filename}")
        
        # 读取CSV文件
        try:
            df = pd.read_csv(input_path, encoding='utf-8')
            print(f"成功读取文件，共 {len(df)} 行数据")
        except Exception as e:
            print(f"读取文件失败: {e}")
            return None
        
        # 检查必要的列是否存在
        required_columns = ['photo', 'data_sources']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"警告: 未找到列: {missing_columns}")
            return None
        
        # 统计修复前的错误URL数量
        photo_before = self.count_url_errors(df['photo'])
        data_sources_before = self.count_url_errors(df['data_sources'])
        
        total_before = (photo_before['https_errors'] + photo_before['http_errors'] + 
                       data_sources_before['https_errors'] + data_sources_before['http_errors'])
        
        print(f"修复前URL格式错误统计:")
        print(f"  photo列:")
        print(f"    - https:-- 错误: {photo_before['https_errors']} 处")
        print(f"    - http:-- 错误: {photo_before['http_errors']} 处")
        print(f"  data_sources列:")
        print(f"    - https:-- 错误: {data_sources_before['https_errors']} 处")
        print(f"    - http:-- 错误: {data_sources_before['http_errors']} 处")
        print(f"  总计错误: {total_before} 处")
        
        if total_before == 0:
            print("未发现URL格式错误，跳过处理")
            return {
                'filename': filename,
                'photo_fixed': 0,
                'data_sources_fixed': 0,
                'total_fixed': 0,
                'total_rows': len(df)
            }
        
        # 修复URL格式
        df['photo'] = df['photo'].apply(self.fix_url_format)
        df['data_sources'] = df['data_sources'].apply(self.fix_url_format)
        
        # 验证修复结果
        photo_after = self.count_url_errors(df['photo'])
        data_sources_after = self.count_url_errors(df['data_sources'])
        
        photo_fixed = (photo_before['https_errors'] + photo_before['http_errors'] - 
                      photo_after['https_errors'] - photo_after['http_errors'])
        data_sources_fixed = (data_sources_before['https_errors'] + data_sources_before['http_errors'] - 
                             data_sources_after['https_errors'] - data_sources_after['http_errors'])
        total_fixed = photo_fixed + data_sources_fixed
        
        total_after = (photo_after['https_errors'] + photo_after['http_errors'] + 
                      data_sources_after['https_errors'] + data_sources_after['http_errors'])
        
        print(f"修复结果:")
        print(f"  photo列修复: {photo_fixed} 处")
        print(f"  data_sources列修复: {data_sources_fixed} 处")
        print(f"  总计修复: {total_fixed} 处")
        print(f"  剩余错误: {total_after} 处")
        
        # 保存修复后的文件
        try:
            df.to_csv(output_path, index=False, encoding='utf-8')
            print(f"文件已保存到: {output_path}")
            
            return {
                'filename': filename,
                'photo_before_https': photo_before['https_errors'],
                'photo_before_http': photo_before['http_errors'],
                'photo_after_https': photo_after['https_errors'],
                'photo_after_http': photo_after['http_errors'],
                'photo_fixed': photo_fixed,
                'data_sources_before_https': data_sources_before['https_errors'],
                'data_sources_before_http': data_sources_before['http_errors'],
                'data_sources_after_https': data_sources_after['https_errors'],
                'data_sources_after_http': data_sources_after['http_errors'],
                'data_sources_fixed': data_sources_fixed,
                'total_fixed': total_fixed,
                'total_rows': len(df)
            }
        except Exception as e:
            print(f"保存文件失败: {e}")
            return None
    
    def generate_report(self, results):
        """生成修复报告"""
        report_path = os.path.join(self.output_folder, "完整URL格式修复报告.txt")
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("photo列和data_sources列URL格式修复报告\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"处理文件夹: {self.input_folder}\n")
            f.write(f"输出文件夹: {self.output_folder}\n\n")
            
            f.write("修复内容:\n")
            f.write("- 将错误的 https:-- 格式修复为 https://\n")
            f.write("- 将错误的 http:-- 格式修复为 http://\n")
            f.write("- 同时处理photo列和data_sources列\n\n")
            
            f.write("处理结果:\n")
            f.write("-" * 50 + "\n")
            
            total_photo_fixed = 0
            total_data_sources_fixed = 0
            total_all_fixed = 0
            successful_files = 0
            
            for result in results:
                if result is not None:
                    successful_files += 1
                    filename = result['filename']
                    photo_fixed = result['photo_fixed']
                    data_sources_fixed = result['data_sources_fixed']
                    total_fixed = result['total_fixed']
                    
                    total_photo_fixed += photo_fixed
                    total_data_sources_fixed += data_sources_fixed
                    total_all_fixed += total_fixed
                    
                    f.write(f"\n{filename}:\n")
                    f.write(f"  photo列修复: {photo_fixed} 处URL格式错误\n")
                    f.write(f"  data_sources列修复: {data_sources_fixed} 处URL格式错误\n")
                    f.write(f"  总计修复: {total_fixed} 处URL格式错误\n")
                else:
                    f.write(f"\n{filename}: 处理失败\n")
            
            f.write(f"\n" + "=" * 50 + "\n")
            f.write(f"总体统计:\n")
            f.write(f"  成功处理文件: {successful_files}/{len(self.files_to_process)}\n")
            f.write(f"  photo列总计修复: {total_photo_fixed} 处URL格式错误\n")
            f.write(f"  data_sources列总计修复: {total_data_sources_fixed} 处URL格式错误\n")
            f.write(f"  全部列总计修复: {total_all_fixed} 处URL格式错误\n")
            
            if successful_files == len(self.files_to_process):
                f.write(f"\n✅ 所有文件URL格式修复完成！\n")
                f.write(f"   共修复了 {total_all_fixed} 处URL格式错误\n")
            else:
                failed_files = len(self.files_to_process) - successful_files
                f.write(f"\n⚠️  {failed_files} 个文件处理失败，请检查错误信息\n")
        
        print(f"\n修复报告已生成: {report_path}")
    
    def run(self):
        """运行URL格式修复程序"""
        print("开始修复photo列和data_sources列URL格式错误...")
        print("=" * 70)
        
        # 创建输出文件夹
        self.create_output_folder()
        
        # 处理所有文件
        results = []
        for filename in self.files_to_process:
            result = self.process_single_file(filename)
            results.append(result)
        
        # 生成报告
        self.generate_report(results)
        
        print("\n" + "=" * 70)
        print("URL格式修复程序执行完成！")

if __name__ == "__main__":
    fixer = AllURLFixer()
    fixer.run()
