#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交换CSV文件中update_time和publish_time列的数据
保持列的位置不变，只交换数据内容
"""

import csv
import os
import shutil
from datetime import datetime

def swap_time_columns(csv_file_path):
    """
    交换CSV文件中第19列(update_time)和第20列(publish_time)的数据
    
    Args:
        csv_file_path (str): CSV文件路径
    """
    
    # 创建备份文件
    backup_path = csv_file_path + f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(csv_file_path, backup_path)
    print(f"已创建备份文件: {backup_path}")
    
    # 读取原始数据
    rows = []
    with open(csv_file_path, 'r', encoding='utf-8') as file:
        csv_reader = csv.reader(file)
        for row in csv_reader:
            rows.append(row)
    
    print(f"读取了 {len(rows)} 行数据")
    
    # 交换数据（跳过表头）
    swap_count = 0
    for i in range(1, len(rows)):  # 从第2行开始（跳过表头）
        if len(rows[i]) >= 20:  # 确保行有足够的列
            # 交换第19列和第20列的数据（索引18和19）
            original_update_time = rows[i][18]
            original_publish_time = rows[i][19]
            
            # 交换数据
            rows[i][18] = original_publish_time  # update_time列现在存放原来的publish_time数据
            rows[i][19] = original_update_time   # publish_time列现在存放原来的update_time数据
            
            swap_count += 1
    
    # 写回文件
    with open(csv_file_path, 'w', encoding='utf-8', newline='') as file:
        csv_writer = csv.writer(file)
        csv_writer.writerows(rows)
    
    print(f"成功交换了 {swap_count} 行的时间数据")
    print(f"交换完成！文件已更新: {csv_file_path}")
    
    return swap_count

def verify_swap(csv_file_path, sample_size=5):
    """
    验证交换结果，显示前几行的时间数据
    
    Args:
        csv_file_path (str): CSV文件路径
        sample_size (int): 显示的样本行数
    """
    print(f"\n验证交换结果（显示前{sample_size}行数据）:")
    print("-" * 80)
    
    with open(csv_file_path, 'r', encoding='utf-8') as file:
        csv_reader = csv.reader(file)
        rows = list(csv_reader)
        
        # 显示表头
        if len(rows) > 0:
            print(f"列名: update_time(第19列) | publish_time(第20列)")
            print("-" * 80)
            
            # 显示样本数据
            for i in range(1, min(sample_size + 1, len(rows))):
                if len(rows[i]) >= 20:
                    exercise_name = rows[i][1][:30] + "..." if len(rows[i][1]) > 30 else rows[i][1]
                    update_time = rows[i][18]
                    publish_time = rows[i][19]
                    print(f"行{i+1}: {exercise_name}")
                    print(f"      update_time: {update_time}")
                    print(f"      publish_time: {publish_time}")
                    print()

def main():
    """主函数"""
    csv_file_path = "csv/东海.csv"
    
    # 检查文件是否存在
    if not os.path.exists(csv_file_path):
        print(f"错误: 文件 {csv_file_path} 不存在")
        return
    
    print("开始交换update_time和publish_time列的数据...")
    print("=" * 60)
    
    try:
        # 执行交换
        swap_count = swap_time_columns(csv_file_path)
        
        # 验证结果
        verify_swap(csv_file_path)
        
        print("=" * 60)
        print("交换操作完成！")
        print(f"总共处理了 {swap_count} 行数据")
        print("\n说明:")
        print("- update_time列(第19列)现在包含原来的publish_time数据")
        print("- publish_time列(第20列)现在包含原来的update_time数据")
        print("- 列的位置保持不变，只是数据内容进行了交换")
        
    except Exception as e:
        print(f"错误: {e}")
        print("请检查文件格式和权限")

if __name__ == "__main__":
    main()
