#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交换CSV文件中update_time和publish_time列的数据
保持列的位置不变，只交换数据内容
"""

import csv
import os
import shutil
from datetime import datetime

def swap_time_columns(csv_file_path, output_dir="已修改1"):
    """
    交换CSV文件中第19列(update_time)和第20列(publish_time)的数据

    Args:
        csv_file_path (str): CSV文件路径
        output_dir (str): 输出目录
    """

    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"已创建输出目录: {output_dir}")

    # 创建备份文件
    backup_path = csv_file_path + f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(csv_file_path, backup_path)
    print(f"已创建备份文件: {backup_path}")
    
    # 读取原始数据
    rows = []
    with open(csv_file_path, 'r', encoding='utf-8') as file:
        csv_reader = csv.reader(file)
        for row in csv_reader:
            rows.append(row)
    
    print(f"读取了 {len(rows)} 行数据")
    
    # 交换数据（跳过表头）
    swap_count = 0
    for i in range(1, len(rows)):  # 从第2行开始（跳过表头）
        if len(rows[i]) >= 20:  # 确保行有足够的列
            # 交换第19列和第20列的数据（索引18和19）
            original_update_time = rows[i][18]
            original_publish_time = rows[i][19]

            # 交换数据
            rows[i][18] = original_publish_time  # update_time列现在存放原来的publish_time数据
            rows[i][19] = original_update_time   # publish_time列现在存放原来的update_time数据

            swap_count += 1
    
    # 生成输出文件路径
    filename = os.path.basename(csv_file_path)
    output_file_path = os.path.join(output_dir, filename)

    # 写入输出文件
    with open(output_file_path, 'w', encoding='utf-8', newline='') as file:
        csv_writer = csv.writer(file)
        csv_writer.writerows(rows)

    print(f"成功交换了 {swap_count} 行的时间数据")
    print(f"交换完成！文件已保存到: {output_file_path}")

    return swap_count, output_file_path

def verify_swap(csv_file_path, sample_size=5):
    """
    验证交换结果，显示前几行的时间数据

    Args:
        csv_file_path (str): CSV文件路径
        sample_size (int): 显示的样本行数
    """
    print(f"\n验证交换结果（显示前{sample_size}行数据）:")
    print("-" * 80)

    with open(csv_file_path, 'r', encoding='utf-8') as file:
        csv_reader = csv.reader(file)
        rows = list(csv_reader)

        # 显示表头
        if len(rows) > 0:
            print(f"列名: update_time(第19列) | publish_time(第20列)")
            print("-" * 80)

            # 显示样本数据
            for i in range(1, min(sample_size + 1, len(rows))):
                if len(rows[i]) >= 20:
                    exercise_name = rows[i][1][:30] + "..." if len(rows[i][1]) > 30 else rows[i][1]
                    update_time = rows[i][18]
                    publish_time = rows[i][19]
                    print(f"行{i+1}: {exercise_name}")
                    print(f"      update_time: {update_time}")
                    print(f"      publish_time: {publish_time}")
                    print()

def main():
    """主函数"""
    # 定义要处理的CSV文件列表
    csv_files = [
        "csv/东海.csv",
        "csv/南海.csv",
        "csv/黄渤海.csv"
    ]

    print("开始交换update_time和publish_time列的数据...")
    print("=" * 60)

    total_processed = 0
    successful_files = []
    failed_files = []

    for csv_file_path in csv_files:
        print(f"\n处理文件: {csv_file_path}")
        print("-" * 40)

        # 检查文件是否存在
        if not os.path.exists(csv_file_path):
            print(f"警告: 文件 {csv_file_path} 不存在，跳过")
            failed_files.append(csv_file_path)
            continue

        try:
            # 执行交换
            swap_count, output_file_path = swap_time_columns(csv_file_path)
            total_processed += swap_count
            successful_files.append(output_file_path)

            # 验证结果
            verify_swap(output_file_path, sample_size=3)

        except Exception as e:
            print(f"错误: 处理文件 {csv_file_path} 时出错: {e}")
            failed_files.append(csv_file_path)

    # 总结报告
    print("\n" + "=" * 60)
    print("批量交换操作完成！")
    print(f"成功处理文件数: {len(successful_files)}")
    print(f"失败文件数: {len(failed_files)}")
    print(f"总共处理数据行数: {total_processed}")

    if successful_files:
        print(f"\n成功处理的文件:")
        for file in successful_files:
            print(f"  ✓ {file}")

    if failed_files:
        print(f"\n处理失败的文件:")
        for file in failed_files:
            print(f"  ✗ {file}")

    print("\n说明:")
    print("- update_time列(第19列)现在包含原来的publish_time数据")
    print("- publish_time列(第20列)现在包含原来的update_time数据")
    print("- 列的位置保持不变，只是数据内容进行了交换")
    print("- 原始文件已创建备份")
    print("- 修改后的文件保存在'已修改1'文件夹中")

if __name__ == "__main__":
    main()
