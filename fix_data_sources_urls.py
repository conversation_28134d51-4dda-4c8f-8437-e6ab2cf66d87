#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复data_sources列中URL格式错误的程序
将 https:-- 和 http:-- 修复为正确的 https:// 和 http://
"""

import pandas as pd
import os
import re
from datetime import datetime

class DataSourcesURLFixer:
    def __init__(self):
        self.input_folder = "已修改3"
        self.output_folder = "已修改4"
        self.files_to_process = ["东海.csv", "南海.csv", "黄渤海.csv"]
        
    def create_output_folder(self):
        """创建输出文件夹"""
        if not os.path.exists(self.output_folder):
            os.makedirs(self.output_folder)
            print(f"创建输出文件夹: {self.output_folder}")
    
    def fix_url_format(self, text):
        """修复URL格式错误"""
        if pd.isna(text) or text == "":
            return text
        
        # 将 https:-- 替换为 https://
        text = re.sub(r'https:--', 'https://', text)
        # 将 http:-- 替换为 http://
        text = re.sub(r'http:--', 'http://', text)
        
        return text
    
    def process_single_file(self, filename):
        """处理单个CSV文件"""
        input_path = os.path.join(self.input_folder, filename)
        output_path = os.path.join(self.output_folder, filename)
        
        print(f"\n处理文件: {filename}")
        
        # 读取CSV文件
        try:
            df = pd.read_csv(input_path, encoding='utf-8')
            print(f"成功读取文件，共 {len(df)} 行数据")
        except Exception as e:
            print(f"读取文件失败: {e}")
            return False
        
        # 检查data_sources列是否存在
        if 'data_sources' not in df.columns:
            print("警告: 未找到data_sources列")
            return False
        
        # 统计修复前的错误URL数量
        original_data = df['data_sources'].astype(str)
        https_errors = original_data.str.contains('https:--', na=False).sum()
        http_errors = original_data.str.contains('http:--', na=False).sum()
        total_errors = https_errors + http_errors
        
        print(f"data_sources列URL格式错误统计:")
        print(f"  - https:-- 错误: {https_errors} 处")
        print(f"  - http:-- 错误: {http_errors} 处")
        print(f"  - 总计错误: {total_errors} 处")
        
        if total_errors == 0:
            print("未发现URL格式错误，跳过处理")
            # 仍然复制文件到输出文件夹
            df.to_csv(output_path, index=False, encoding='utf-8')
            return True
        
        # 修复data_sources列中的URL格式
        df['data_sources'] = df['data_sources'].apply(self.fix_url_format)
        
        # 验证修复结果
        fixed_data = df['data_sources'].astype(str)
        remaining_https_errors = fixed_data.str.contains('https:--', na=False).sum()
        remaining_http_errors = fixed_data.str.contains('http:--', na=False).sum()
        remaining_total = remaining_https_errors + remaining_http_errors
        
        fixed_count = total_errors - remaining_total
        
        print(f"修复结果:")
        print(f"  - 修复了 {fixed_count} 处URL格式错误")
        print(f"  - 剩余错误: {remaining_total} 处")
        
        # 保存修复后的文件
        try:
            df.to_csv(output_path, index=False, encoding='utf-8')
            print(f"文件已保存到: {output_path}")
            return True
        except Exception as e:
            print(f"保存文件失败: {e}")
            return False
    
    def generate_report(self, results):
        """生成修复报告"""
        report_path = os.path.join(self.output_folder, "data_sources列URL格式修复报告.txt")
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("data_sources列URL格式修复报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"处理文件夹: {self.input_folder}\n")
            f.write(f"输出文件夹: {self.output_folder}\n\n")
            
            f.write("修复内容:\n")
            f.write("- 将错误的 https:-- 格式修复为 https://\n")
            f.write("- 将错误的 http:-- 格式修复为 http://\n")
            f.write("- 仅处理data_sources列\n\n")
            
            f.write("处理结果:\n")
            f.write("-" * 30 + "\n")
            
            total_files = len(results)
            successful_files = sum(1 for result in results.values() if result)
            
            for filename, success in results.items():
                status = "成功" if success else "失败"
                f.write(f"{filename}: {status}\n")
            
            f.write(f"\n总计: {successful_files}/{total_files} 个文件处理成功\n")
            
            if successful_files == total_files:
                f.write("\n✅ 所有文件data_sources列URL格式修复完成！\n")
            else:
                f.write(f"\n⚠️  {total_files - successful_files} 个文件处理失败，请检查错误信息\n")
        
        print(f"\n修复报告已生成: {report_path}")
    
    def run(self):
        """运行URL格式修复程序"""
        print("开始修复data_sources列URL格式错误...")
        print("=" * 60)
        
        # 创建输出文件夹
        self.create_output_folder()
        
        # 处理所有文件
        results = {}
        for filename in self.files_to_process:
            results[filename] = self.process_single_file(filename)
        
        # 生成报告
        self.generate_report(results)
        
        print("\n" + "=" * 60)
        print("data_sources列URL格式修复程序执行完成！")

if __name__ == "__main__":
    fixer = DataSourcesURLFixer()
    fixer.run()
