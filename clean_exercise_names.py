#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
军事演习名称标准化清理程序
Military Exercise Name Standardization Tool

基于国际军事演习命名规则，对演习名称进行标准化处理
输入：已修改2文件夹中的CSV文件
输出：已修改3文件夹中的清理后CSV文件

命名规则说明：
1. 移除冗余日期信息（年份、具体日期）
2. 标准化演习编号和代号
3. 保留核心演习标识和地理信息
4. 统一格式和引号使用
5. 保持国际军事演习命名标准的专业性

作者：AI Assistant
日期：2025-01-29
"""

import csv
import re
import os
import shutil
from datetime import datetime
from typing import List, Tuple, Dict

class ExerciseNameCleaner:
    """军事演习名称清理器"""
    
    def __init__(self):
        self.input_dir = "已修改2"
        self.output_dir = "已修改3"
        self.backup_dir = "备份"
        self.cleaning_rules = self._initialize_cleaning_rules()
        self.cleaning_report = []
        
    def _initialize_cleaning_rules(self) -> List[Tuple[str, str, str]]:
        """
        初始化清理规则
        返回：(模式, 替换, 说明) 的列表
        """
        rules = [
            # 1. 移除具体日期前缀
            (r'^(\d{4}年\d{1,2}月\d{1,2}日)', '', '移除具体日期前缀'),
            (r'^(\d{4}年\d{1,2}月\d{1,2}至\d{1,2}日)', '', '移除日期范围前缀'),
            
            # 2. 标准化年份标识
            (r'"([^"]+)\s*\d{2,4}[号]?"', r'"\1"', '移除演习名称中的年份编号'),
            (r'([^"]+)\s*\d{2,4}[号]?演习', r'\1演习', '移除演习后缀中的年份'),
            
            # 3. 清理训练测考编号
            (r'"三军联合作战训练测考\d{3}之\d+号[、，]\d*号?"', '"三军联合作战训练测考"', '标准化训练测考编号'),
            
            # 4. 移除年份前缀
            (r'^台(\d{4})', '台', '移除台湾演习年份前缀'),
            (r'^(\d{4}年\d{1,2}月\d{1,2}至\d{1,2}日)', '', '移除完整日期前缀'),
            
            # 5. 标准化引号
            (r'[""]([^"""]+)[""]', r'"\1"', '统一引号格式'),
            
            # 6. 清理多余空格
            (r'\s+', ' ', '清理多余空格'),
            (r'^\s+|\s+$', '', '清理首尾空格'),
        ]
        return rules
    
    def clean_exercise_name(self, original_name: str) -> Tuple[str, List[str]]:
        """
        清理单个演习名称
        
        Args:
            original_name: 原始演习名称
            
        Returns:
            (清理后名称, 应用的规则列表)
        """
        if not original_name or original_name.strip() == '':
            return original_name, []
            
        cleaned_name = original_name.strip()
        applied_rules = []
        
        # 应用清理规则
        for pattern, replacement, description in self.cleaning_rules:
            if re.search(pattern, cleaned_name):
                cleaned_name = re.sub(pattern, replacement, cleaned_name)
                applied_rules.append(description)
        
        # 特殊处理：移除特定的年份模式
        special_patterns = [
            # 处理类似 "利剑23"、"利剑24" 的情况
            (r'(利剑|铁拳|对抗北方|应对北方|海龙)\s*\d{2,4}', r'\1', '移除代号中的年份'),
            # 处理类似 "汉光37号"、"汉光38号" 的情况
            (r'(汉光|天龙操演|春节战备操演)\s*\d{2,4}号?', r'\1', '移除编号中的年份'),
            # 处理日期格式的演习名
            (r'^(\d{4}年\d{1,2}月\d{1,2}至?\d*日?)', '', '移除开头日期'),
        ]
        
        for pattern, replacement, description in special_patterns:
            if re.search(pattern, cleaned_name):
                cleaned_name = re.sub(pattern, replacement, cleaned_name)
                applied_rules.append(description)
        
        # 最终清理
        cleaned_name = re.sub(r'\s+', ' ', cleaned_name).strip()
        
        return cleaned_name, applied_rules
    
    def create_output_directory(self):
        """创建输出目录"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            print(f"创建输出目录: {self.output_dir}")
    
    def process_csv_file(self, filename: str) -> Dict:
        """
        处理单个CSV文件
        
        Args:
            filename: CSV文件名
            
        Returns:
            处理统计信息
        """
        input_path = os.path.join(self.input_dir, filename)
        output_path = os.path.join(self.output_dir, filename)
        
        if not os.path.exists(input_path):
            print(f"文件不存在: {input_path}")
            return {}
        
        stats = {
            'filename': filename,
            'total_rows': 0,
            'cleaned_rows': 0,
            'unchanged_rows': 0,
            'changes': []
        }
        
        try:
            # 读取和处理CSV文件
            with open(input_path, 'r', encoding='utf-8', newline='') as infile, \
                 open(output_path, 'w', encoding='utf-8', newline='') as outfile:
                
                reader = csv.reader(infile)
                writer = csv.writer(outfile)
                
                # 处理标题行
                headers = next(reader)
                writer.writerow(headers)
                
                # 找到exercise_name列的索引
                try:
                    exercise_name_index = headers.index('exercise_name')
                except ValueError:
                    print(f"警告: 在文件 {filename} 中未找到 'exercise_name' 列")
                    # 直接复制文件
                    shutil.copy2(input_path, output_path)
                    return stats
                
                # 处理数据行
                for row_num, row in enumerate(reader, start=2):
                    stats['total_rows'] += 1
                    
                    if len(row) > exercise_name_index:
                        original_name = row[exercise_name_index]
                        cleaned_name, applied_rules = self.clean_exercise_name(original_name)
                        
                        if cleaned_name != original_name:
                            stats['cleaned_rows'] += 1
                            change_info = {
                                'row': row_num,
                                'original': original_name,
                                'cleaned': cleaned_name,
                                'rules': applied_rules
                            }
                            stats['changes'].append(change_info)
                            row[exercise_name_index] = cleaned_name
                        else:
                            stats['unchanged_rows'] += 1
                    
                    writer.writerow(row)
                    
        except Exception as e:
            print(f"处理文件 {filename} 时出错: {str(e)}")
            return {}
        
        print(f"✓ 处理完成: {filename}")
        print(f"  总行数: {stats['total_rows']}")
        print(f"  清理行数: {stats['cleaned_rows']}")
        print(f"  未变更行数: {stats['unchanged_rows']}")
        
        return stats
    
    def generate_cleaning_report(self, all_stats: List[Dict]):
        """生成清理报告"""
        report_path = os.path.join(self.output_dir, "演习名称清理报告.txt")
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("军事演习名称标准化清理报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 总体统计
            total_files = len(all_stats)
            total_rows = sum(stats.get('total_rows', 0) for stats in all_stats)
            total_cleaned = sum(stats.get('cleaned_rows', 0) for stats in all_stats)
            
            f.write("总体统计:\n")
            f.write(f"处理文件数: {total_files}\n")
            f.write(f"总数据行数: {total_rows}\n")
            f.write(f"清理行数: {total_cleaned}\n")
            f.write(f"清理比例: {total_cleaned/total_rows*100:.1f}%\n\n")
            
            # 详细变更记录
            f.write("详细变更记录:\n")
            f.write("-" * 30 + "\n")
            
            for stats in all_stats:
                if not stats or 'changes' not in stats:
                    continue
                    
                f.write(f"\n文件: {stats['filename']}\n")
                
                for change in stats['changes'][:10]:  # 只显示前10个变更
                    f.write(f"  行 {change['row']}:\n")
                    f.write(f"    原名称: {change['original']}\n")
                    f.write(f"    新名称: {change['cleaned']}\n")
                    f.write(f"    应用规则: {', '.join(change['rules'])}\n\n")
                
                if len(stats['changes']) > 10:
                    f.write(f"  ... 还有 {len(stats['changes']) - 10} 个变更\n\n")
        
        print(f"✓ 清理报告已生成: {report_path}")
    
    def run(self):
        """运行清理程序"""
        print("军事演习名称标准化清理程序")
        print("=" * 40)
        print()
        
        # 检查输入目录
        if not os.path.exists(self.input_dir):
            print(f"错误: 输入目录不存在 - {self.input_dir}")
            return
        
        # 创建输出目录
        self.create_output_directory()
        
        # 获取CSV文件列表
        csv_files = [f for f in os.listdir(self.input_dir) if f.endswith('.csv')]
        
        if not csv_files:
            print(f"在目录 {self.input_dir} 中未找到CSV文件")
            return
        
        print(f"找到 {len(csv_files)} 个CSV文件:")
        for f in csv_files:
            print(f"  - {f}")
        print()
        
        # 处理所有文件
        all_stats = []
        for filename in csv_files:
            print(f"正在处理: {filename}")
            stats = self.process_csv_file(filename)
            if stats:
                all_stats.append(stats)
        
        # 生成报告
        print("\n生成清理报告...")
        self.generate_cleaning_report(all_stats)
        
        print(f"\n✓ 所有文件处理完成！")
        print(f"输出目录: {self.output_dir}")

if __name__ == "__main__":
    cleaner = ExerciseNameCleaner()
    cleaner.run()
