军事演习名称标准化清理报告
==================================================
生成时间: 2025-07-29 11:30:47

总体统计:
处理文件数: 3
总数据行数: 350
清理行数: 84
清理比例: 24.0%

详细变更记录:
------------------------------

文件: 东海.csv
  行 2:
    原名称: 美日“利剑23”演习
    新名称: 美日“利剑”演习
    应用规则: 移除代号中的年份

  行 3:
    原名称: 美日“利剑24”演习
    新名称: 美日“利剑”演习
    应用规则: 移除代号中的年份

  行 10:
    原名称: 台湾“汉光37号”演习
    新名称: 台湾“汉光”演习
    应用规则: 移除编号中的年份

  行 11:
    原名称: 台湾“汉光38号”演习
    新名称: 台湾“汉光”演习
    应用规则: 移除编号中的年份

  行 12:
    原名称: 台湾“汉光39号”演习
    新名称: 台湾“汉光”演习
    应用规则: 移除编号中的年份

  行 13:
    原名称: 台湾“汉光40号”演习
    新名称: 台湾“汉光”演习
    应用规则: 移除编号中的年份

  行 16:
    原名称: 台2021空军“天龙操演”演习
    新名称: 台空军“天龙操演”演习
    应用规则: 移除台湾演习年份前缀

  行 17:
    原名称: 台2022空军“天龙操演”演习
    新名称: 台空军“天龙操演”演习
    应用规则: 移除台湾演习年份前缀

  行 18:
    原名称: 台2023空军“天龙操演”演习
    新名称: 台空军“天龙操演”演习
    应用规则: 移除台湾演习年份前缀

  行 19:
    原名称: 台2024空军“天龙操演”演习
    新名称: 台空军“天龙操演”演习
    应用规则: 移除台湾演习年份前缀

  ... 还有 26 个变更


文件: 南海.csv
  行 94:
    原名称: 2023年8月23日美国-马来西亚联合演习
    新名称: 美国-马来西亚联合演习
    应用规则: 移除具体日期前缀

  行 95:
    原名称: 2023年8月24日美日澳菲联合演习
    新名称: 美日澳菲联合演习
    应用规则: 移除具体日期前缀

  行 96:
    原名称: 2023年9月4日日本海上自卫队反潜战演习
    新名称: 日本海上自卫队反潜战演习
    应用规则: 移除具体日期前缀

  行 97:
    原名称: 2023年9月4日美菲双边演习
    新名称: 美菲双边演习
    应用规则: 移除具体日期前缀

  行 98:
    原名称: 2023年9月17日美英双边演习
    新名称: 美英双边演习
    应用规则: 移除具体日期前缀

  行 100:
    原名称: 2023年9月19日加菲联合演习
    新名称: 加菲联合演习
    应用规则: 移除具体日期前缀

  行 102:
    原名称: 2023年11月21至23日美菲联合巡逻
    新名称: 美菲联合巡逻
    应用规则: 移除日期范围前缀

  行 104:
    原名称: 2023年1月13日“尼米兹”号航母打击群演习
    新名称: “尼米兹”号航母打击群演习
    应用规则: 移除具体日期前缀

  行 106:
    原名称: 2023年6月1至7日美日菲联合演习
    新名称: 美日菲联合演习
    应用规则: 移除日期范围前缀

  行 107:
    原名称: 2023年6月9日美日法加综合海上演习
    新名称: 美日法加综合海上演习
    应用规则: 移除具体日期前缀

  ... 还有 37 个变更


文件: 黄渤海.csv
  行 13:
    原名称: “多国水雷战”22演习
    新名称: “多国水雷战”演习
    应用规则: 移除演习后缀中的年份

